#!/usr/bin/env python3
"""
测试脚本：验证修改后的Emby保活逻辑
"""

import sys
import os
from datetime import datetime, timedelta
from dateutil import parser

# 添加embykeeper路径
sys.path.insert(0, 'python-3.8.10-embed-amd64/Lib/site-packages')

def test_should_run_emby_keepalive():
    """测试智能判断函数"""
    try:
        from embykeeper.cli import should_run_emby_keepalive
        from embykeeper.cache import cache
        
        print("=== 测试Emby保活智能判断逻辑 ===")
        
        # 测试1: 没有缓存的情况（首次运行）
        print("\n测试1: 清空缓存，模拟首次运行")
        try:
            cache.delete("scheduler.emby.watch.global")
        except:
            pass
        
        result = should_run_emby_keepalive()
        print(f"结果: {'应该执行' if result else '不应该执行'}")
        
        # 测试2: 设置未来时间的缓存
        print("\n测试2: 设置未来时间的缓存")
        future_time = datetime.now() + timedelta(days=5)
        cache.set("scheduler.emby.watch.global", {
            "config_hash": "test_hash",
            "next_time": future_time.isoformat(),
            "description": "测试Emby保活任务"
        })
        
        result = should_run_emby_keepalive()
        print(f"结果: {'应该执行' if result else '不应该执行'}")
        
        # 测试3: 设置过去时间的缓存
        print("\n测试3: 设置过去时间的缓存")
        past_time = datetime.now() - timedelta(hours=1)
        cache.set("scheduler.emby.watch.global", {
            "config_hash": "test_hash",
            "next_time": past_time.isoformat(),
            "description": "测试Emby保活任务"
        })
        
        result = should_run_emby_keepalive()
        print(f"结果: {'应该执行' if result else '不应该执行'}")
        
        # 清理测试缓存
        try:
            cache.delete("scheduler.emby.watch.global")
        except:
            pass
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import():
    """测试导入是否正常"""
    try:
        print("=== 测试导入 ===")
        from embykeeper.cli import should_run_emby_keepalive
        print("✓ 成功导入 should_run_emby_keepalive 函数")
        
        from embykeeper.cache import cache
        print("✓ 成功导入 cache 模块")
        
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修改后的Emby保活逻辑...")
    
    # 测试导入
    if not test_import():
        print("导入测试失败，退出")
        sys.exit(1)
    
    # 测试逻辑
    if test_should_run_emby_keepalive():
        print("所有测试通过！")
    else:
        print("测试失败！")
        sys.exit(1)
