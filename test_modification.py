#!/usr/bin/env python3
"""
简单测试：验证修改后的代码语法正确性
"""

import ast
import sys

def test_syntax():
    """测试修改后的cli.py文件语法是否正确"""
    try:
        with open('python-3.8.10-embed-amd64/Lib/site-packages/embykeeper/cli.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 尝试解析AST
        ast.parse(code)
        print("✓ cli.py 语法检查通过")
        
        # 检查关键函数是否存在
        if 'def should_run_emby_keepalive():' in code:
            print("✓ should_run_emby_keepalive 函数已添加")
        else:
            print("✗ should_run_emby_keepalive 函数未找到")
            return False
        
        # 检查修改后的逻辑是否存在
        if 'if emby_man and should_run_emby_keepalive():' in code:
            print("✓ 智能判断逻辑已添加")
        else:
            print("✗ 智能判断逻辑未找到")
            return False

        # 检查缓存更新函数是否存在
        if 'def update_emby_keepalive_cache():' in code:
            print("✓ 缓存更新函数已添加")
        else:
            print("✗ 缓存更新函数未找到")
            return False

        # 检查缓存更新调用是否存在
        if 'update_emby_keepalive_cache()' in code:
            print("✓ 缓存更新调用已添加")
        else:
            print("✗ 缓存更新调用未找到")
            return False
        
        # 检查导入是否正确
        if 'from datetime import datetime' in code:
            print("✓ datetime 导入已添加")
        else:
            print("✗ datetime 导入未找到")
            return False
        
        if 'from dateutil import parser' in code:
            print("✓ dateutil.parser 导入已添加")
        else:
            print("✗ dateutil.parser 导入未找到")
            return False
        
        return True
        
    except SyntaxError as e:
        print(f"✗ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_logic_structure():
    """测试逻辑结构是否正确"""
    try:
        with open('python-3.8.10-embed-amd64/Lib/site-packages/embykeeper/cli.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        print("\n=== 检查逻辑结构 ===")
        
        # 检查智能判断函数的结构
        if 'from .cache import cache' in code:
            print("✓ 缓存模块导入正确")
        
        if 'scheduler.emby.watch.global' in code:
            print("✓ 统一账号调度器缓存键正确")
        
        if 'scheduler.emby.watch.' in code:
            print("✓ 独立账号调度器缓存前缀正确")
        
        if 'logger.info(f"Emby保活任务已到执行时间' in code:
            print("✓ 执行时间到达日志正确")
        
        if 'logger.info(f"Emby保活任务尚未到执行时间' in code:
            print("✓ 执行时间未到日志正确")
        
        # 检查CLI逻辑修改
        if '# 签到任务总是立即执行' in code:
            print("✓ 签到任务注释正确")
        
        if '# Emby保活任务根据调度器缓存智能判断是否执行' in code:
            print("✓ Emby保活任务注释正确")

        if '# 在开始执行保活任务时立即更新缓存' in code:
            print("✓ 缓存更新注释正确")

        if '已更新Emby保活缓存，下一次执行将在' in code:
            print("✓ 缓存更新日志正确")

        return True
        
    except Exception as e:
        print(f"✗ 检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试修改后的代码...")
    
    success = True
    
    # 测试语法
    if not test_syntax():
        success = False
    
    # 测试逻辑结构
    if not test_logic_structure():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！修改成功完成。")
        print("\n修改总结:")
        print("1. ✓ 添加了 should_run_emby_keepalive() 智能判断函数")
        print("2. ✓ 添加了 update_emby_keepalive_cache() 缓存更新函数")
        print("3. ✓ 修改了 --instant 参数的逻辑")
        print("4. ✓ 签到任务现在总是立即执行")
        print("5. ✓ Emby保活任务根据7-12天间隔智能判断是否执行")
        print("6. ✓ 缓存现在在开始执行保活任务时立即更新")
        print("\n使用方法:")
        print("运行 'Embykeeper.bat' 时:")
        print("- 签到任务会立即执行")
        print("- Emby保活任务只有在到达执行时间时才会执行")
        print("- 一旦开始执行保活任务，立即设置下次执行时间（7-12天后）")
    else:
        print("\n❌ 测试失败，请检查修改。")
        sys.exit(1)
